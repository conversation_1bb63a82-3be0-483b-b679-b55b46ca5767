// Navigation and dropdown interactions (moved from inline script)
(function() {
  function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    const menuIcon = document.getElementById('menu-icon');
    const closeIcon = document.getElementById('close-icon');

    if (!mobileMenu || !menuIcon || !closeIcon) return;

    if (mobileMenu.classList.contains('hidden')) {
      mobileMenu.classList.remove('hidden');
      menuIcon.classList.add('hidden');
      closeIcon.classList.remove('hidden');
    } else {
      mobileMenu.classList.add('hidden');
      menuIcon.classList.remove('hidden');
      closeIcon.classList.add('hidden');
      // Close mobile dropdown when closing main menu
      const mobileDropdown = document.getElementById('mobile-dropdown-content');
      const mobileArrow = document.getElementById('mobile-dropdown-arrow');
      if (mobileDropdown) mobileDropdown.classList.remove('expanded');
      if (mobileArrow) mobileArrow.style.transform = 'rotate(0deg)';
    }
  }

  function toggleMobileDropdown() {
    const dropdown = document.getElementById('mobile-dropdown-content');
    const arrow = document.getElementById('mobile-dropdown-arrow');
    const button = arrow ? arrow.parentElement : null;

    if (!dropdown || !arrow || !button) return;

    if (dropdown.classList.contains('expanded')) {
      dropdown.classList.remove('expanded');
      arrow.style.transform = 'rotate(0deg)';
      button.setAttribute('aria-expanded', 'false');
    } else {
      dropdown.classList.add('expanded');
      arrow.style.transform = 'rotate(180deg)';
      button.setAttribute('aria-expanded', 'true');
    }
  }

  // expose handlers for HTML onclick attributes
  window.toggleMobileMenu = toggleMobileMenu;
  window.toggleMobileDropdown = toggleMobileDropdown;

  // Close mobile menu when window is resized to desktop
  window.addEventListener('resize', function() {
    if (window.innerWidth >= 768) {
      const mobileMenu = document.getElementById('mobile-menu');
      const menuIcon = document.getElementById('menu-icon');
      const closeIcon = document.getElementById('close-icon');
      if (mobileMenu) mobileMenu.classList.add('hidden');
      if (menuIcon) menuIcon.classList.remove('hidden');
      if (closeIcon) closeIcon.classList.add('hidden');

      // Reset mobile dropdown
      const mobileDropdown = document.getElementById('mobile-dropdown-content');
      const mobileArrow = document.getElementById('mobile-dropdown-arrow');
      if (mobileDropdown) mobileDropdown.classList.remove('expanded');
      if (mobileArrow) mobileArrow.style.transform = 'rotate(0deg)';
    }
  });

  // Keyboard navigation for dropdown
  document.addEventListener('keydown', function(e) {
    if (e.key !== 'Escape') return;
    const dropdown = document.querySelector('.dropdown');
    const dropdownMenu = document.querySelector('.dropdown-menu');
    if (!dropdown || !dropdownMenu) return;
    dropdownMenu.classList.remove('show');
    const trigger = dropdown.querySelector('button, a');
    if (trigger) trigger.setAttribute('aria-expanded', 'false');
  });

  // Toggle desktop services dropdown on focus/blur and click
  const servicesTrigger = document.querySelector('.dropdown > button');
  const servicesMenu = document.getElementById('services-menu');

  if (servicesTrigger && servicesMenu) {
    servicesTrigger.addEventListener('click', function() {
      const isOpen = servicesMenu.classList.contains('show');
      servicesMenu.classList.toggle('show', !isOpen);
      servicesTrigger.setAttribute('aria-expanded', String(!isOpen));
    });

    servicesTrigger.addEventListener('focus', function() {
      servicesTrigger.setAttribute('aria-expanded', 'true');
      servicesMenu.classList.add('show');
    });

    servicesTrigger.addEventListener('blur', function() {
      setTimeout(() => {
        if (!servicesMenu.contains(document.activeElement)) {
          servicesTrigger.setAttribute('aria-expanded', 'false');
          servicesMenu.classList.remove('show');
        }
      }, 100);
    });

    servicesMenu.querySelectorAll('.dropdown-item').forEach(item => {
      item.addEventListener('blur', function() {
        setTimeout(() => {
          if (!servicesMenu.contains(document.activeElement)) {
            servicesTrigger.setAttribute('aria-expanded', 'false');
            servicesMenu.classList.remove('show');
          }
        }, 100);
      });
    });
  }
})();
