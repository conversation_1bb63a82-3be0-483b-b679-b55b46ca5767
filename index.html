<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://use.typekit.net/oph7lmk.css"> <!-- Acumin Variable Concept via Adobe Fonts -->
  <link rel="stylesheet" href="style.css">
  <script src="https://cdn.tailwindcss.com"></script>
  <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
  <title>Nirmanah Design & Build</title>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- Responsive Navbar (Alpine-powered, desktop styling preserved) -->
  <header class="py-4 bg-white sticky top-0 z-50" x-data="{ expanded: false, servicesOpen: false }">
    <div class="px-8">
      <div class="flex items-center justify-between">
        <!-- Logo and Tagline -->
        <div class="flex items-center space-x-2">
          <img src="logo.png" alt="Nirmanah Logo" class="w-10 h-10 object-contain">
          <div class="flex flex-col">
            <span class="text-xl text-gray-900 nirmanah-font">nirmanah</span>
            <span class="tagline">design & build</span>
          </div>
        </div>

        <!-- Desktop Nav (unchanged look) -->
        <ul class="hidden md:flex items-center space-x-1 nav-links bg-gray-900 rounded-full p-2 shadow-md">
          <li>
            <a href="#" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm text-gray-300">Home</a>
          </li>
          <li>
            <a href="#" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm text-gray-300">About</a>
          </li>
          <li class="dropdown">
            <a href="#" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm flex items-center text-gray-300"
               aria-haspopup="true" aria-expanded="false" role="button">
              Services
              <svg class="dropdown-arrow w-3 h-3 fill-current" viewBox="0 0 12 12">
                <path d="M6 8l4-4H2z"/>
              </svg>
            </a>
            <div class="dropdown-menu" role="menu">
              <a href="#" class="dropdown-item" role="menuitem">Design & Consultancy</a>
              <a href="#" class="dropdown-item" role="menuitem">Renovation & Remodeling</a>
              <a href="#" class="dropdown-item" role="menuitem">Landscaping & Outdoor Work</a>
              <a href="#" class="dropdown-item" role="menuitem">Turnkey Projects</a>
            </div>
          </li>
          <li>
            <a href="#" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm text-gray-300">Portfolio</a>
          </li>
          <li>
            <a href="#" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm text-gray-300">Contact</a>
          </li>
        </ul>

        <!-- Mobile Menu Button -->
        <div class="md:hidden">
          <button type="button" class="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-gray-700" @click="expanded = !expanded" :aria-expanded="expanded" aria-label="Toggle mobile menu">
            <span x-show="!expanded" aria-hidden="true">
              <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </span>
            <span x-show="expanded" aria-hidden="true">
              <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </span>
          </button>
        </div>

        <!-- Get a Quote Button -->
        <div class="hidden md:block">
          <button class="btn-primary">
            Get a Quote
          </button>
        </div>
      </div>

      <!-- Mobile Nav (Alpine x-collapse) -->
      <nav x-show="expanded" x-collapse>
        <div id="mobile-menu" class="md:hidden bg-white border-b shadow-lg">
          <div class="px-8 py-4 space-y-4">
            <a href="#" class="block py-2 text-gray-900 font-medium border-l-4 border-orange-500 pl-4 bg-gray-50 mobile-menu-link active">
              Home
            </a>
            <a href="#" class="block py-2 text-gray-600 hover:text-orange-500 transition-colors duration-200 mobile-menu-link">About</a>

            <!-- Mobile Services Dropdown -->
            <div class="mobile-dropdown">
              <button @click="servicesOpen = !servicesOpen" :aria-expanded="servicesOpen.toString()" class="flex items-center justify-between w-full py-2 text-gray-600 hover:text-orange-500 transition-colors duration-200 mobile-menu-link">
                Services
                <svg class="w-4 h-4 transition-transform duration-300 transform" :class="{ 'rotate-180': servicesOpen }" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
              <div class="mobile-dropdown-content" :class="{ 'expanded': servicesOpen }">
                <a href="#" class="block mobile-dropdown-item px-2 py-2">Design & Consultancy</a>
                <a href="#" class="block mobile-dropdown-item px-2 py-2">Renovation & Remodeling</a>
                <a href="#" class="block mobile-dropdown-item px-2 py-2">Landscaping & Outdoor Work</a>
                <a href="#" class="block mobile-dropdown-item px-2 py-2">Turnkey Projects</a>
              </div>
            </div>

            <a href="#" class="block py-2 text-gray-600 hover:text-orange-500 transition-colors duration-200 mobile-menu-link">Portfolio</a>
            <a href="#" class="block py-2 text-gray-600 hover:text-orange-500 transition-colors duration-200 mobile-menu-link">Contact</a>
            <hr class="my-4">
            <div class="space-y-3">
              <button class="block w-full py-3 btn-primary rounded-full font-medium">
                Get a Quote
              </button>
            </div>
          </div>
        </div>
      </nav>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="relative pt-12 overflow-hidden bg-white sm:pt-16">
      <div class="relative px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
          <div class="max-w-4xl mx-auto text-center">
              <p class="text-sm font-normal tracking-widest uppercase">
                  <span class="text-transparent bg-clip-text" style="background-image: linear-gradient(to right, var(--color-primary), var(--color-secondary-500));"> Design & Build Excellence </span>
              </p>
              <h1 class="mt-8 text-4xl font-normal sm:text-5xl lg:text-6xl xl:text-7xl" style="color: var(--text-primary);">Transform Your Space with Nirmanah</h1>
              <p class="mt-6 text-lg sm:text-xl" style="color: var(--text-secondary);">From concept to completion, we create stunning spaces that reflect your vision and enhance your lifestyle.</p>

              <div class="flex flex-col items-center justify-center px-8 mt-12 space-y-5 sm:space-y-0 sm:px-0 sm:space-x-5 sm:flex-row">
                  <div class="relative inline-flex items-center justify-center w-full sm:w-auto group">
                      <div class="absolute transition-all duration-200 rounded-full -inset-px" style="background: linear-gradient(to right, var(--color-primary), var(--color-secondary-500)); box-shadow: 0 4px 14px 0 rgba(212, 148, 30, 0.25);"></div>
                      <a href="#" title="" class="relative inline-flex items-center justify-center w-full px-8 py-3 text-base font-normal border border-transparent rounded-full sm:w-auto" style="background: linear-gradient(135deg, var(--color-primary), var(--color-secondary-500)); color: var(--text-inverse);" role="button"> Get Your Quote </a>
                  </div>

                  <a href="#" title="" class="inline-flex items-center justify-center w-full px-8 py-3 text-base font-normal transition-all duration-200 border rounded-full sm:w-auto hover:border-opacity-80" style="color: var(--color-primary); border-color: var(--color-primary);" role="button"> View Portfolio </a>
              </div>
          </div>

          <!-- <div class="relative mt-12 -mb-4 sm:-mb-10 lg:-mb-12 sm:mt-16 lg:mt-24">
              <div class="absolute top-0 transform -translate-x-1/2 left-1/2">
                  <svg class="blur-3xl filter" style="filter: blur(64px)" width="645" height="413" viewBox="0 0 645 413" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M181.316 218.778C86.2529 123.715 -63.7045 134.94 31.3589 39.8762C126.422 -55.1873 528.427 41.1918 623.49 136.255C718.554 231.319 470.678 289.068 375.614 384.131C280.551 479.195 276.38 313.842 181.316 218.778Z" fill="url(#nirmanah-gradient)" />
                      <defs>
                          <linearGradient id="nirmanah-gradient" x1="665.741" y1="178.506" x2="296.286" y2="474.62" gradientUnits="userSpaceOnUse">
                              <stop offset="0%" style="stop-color: var(--color-primary)" />
                              <stop offset="100%" style="stop-color: var(--color-secondary-500)" />
                          </linearGradient>
                      </defs>
                  </svg>
              </div>

               <div class="absolute inset-0">
                  <img class="object-cover w-full h-full opacity-30" src="https://landingfoliocom.imgix.net/store/collection/dusk/images/noise.png" alt="" />
              </div> 

              <div class="relative">
                  <img class="relative w-full max-w-5xl mx-auto rounded-lg shadow-2xl" src="iqbal-putra-gBTb_dnfJGw-unsplash.jpg" alt="Modern architectural design showcasing Nirmanah's design and build expertise" />
                  <div class="absolute inset-0 rounded-lg" style="background: linear-gradient(to top, rgba(23, 23, 23, 0.3), transparent);"></div>
              </div> 
          </div> -->
      </div>
  </section>

  <script>
    // Keyboard navigation for dropdown
    document.addEventListener('keydown', function(e) {
      const dropdown = document.querySelector('.dropdown');
      const dropdownMenu = document.querySelector('.dropdown-menu');

      if (e.key === 'Escape' && dropdown && dropdownMenu) {
        dropdownMenu.classList.remove('show');
        const trigger = dropdown.querySelector('a');
        if (trigger) trigger.setAttribute('aria-expanded', 'false');
      }
    });

    // Handle dropdown focus for accessibility
    document.querySelectorAll('.dropdown > a').forEach(trigger => {
      trigger.addEventListener('focus', function() {
        this.setAttribute('aria-expanded', 'true');
        this.nextElementSibling.classList.add('show');
      });

      trigger.addEventListener('blur', function(e) {
        // Delay to check if focus moved to dropdown item
        setTimeout(() => {
          if (!this.nextElementSibling.contains(document.activeElement)) {
            this.setAttribute('aria-expanded', 'false');
            this.nextElementSibling.classList.remove('show');
          }
        }, 100);
      });
    });

    // Handle dropdown item focus
    document.querySelectorAll('.dropdown-item').forEach(item => {
      item.addEventListener('blur', function(e) {
        const dropdown = this.closest('.dropdown');
        const trigger = dropdown.querySelector('a');

        setTimeout(() => {
          if (!dropdown.contains(document.activeElement)) {
            trigger.setAttribute('aria-expanded', 'false');
            const menu = dropdown.querySelector('.dropdown-menu');
            if (menu) menu.classList.remove('show');
          }
        }, 100);
      });
    });
  </script>
</body>
</html>
